package services

import (
	"context"
	"database/sql"
	"fmt"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/services/task_log"
	"log/slog"
	"strings"
	"time"
)

// MailboxManagementService 邮箱管理服务
type MailboxManagementService struct {
	db             *database.Database
	mailManager    *manager.MailManager
	auth           *auth.Auth
	taskLogService *task_log.TaskLogService
	logger         *slog.Logger
}

// NewMailboxManagementService 创建邮箱管理服务
func NewMailboxManagementService(db *database.Database, mailManager *manager.MailManager, auth *auth.Auth, taskLogService *task_log.TaskLogService) *MailboxManagementService {
	return &MailboxManagementService{
		db:             db,
		mailManager:    mailManager,
		auth:           auth,
		taskLogService: taskLogService,
		logger:         slog.Default(),
	}
}

// BatchImportAccounts 批量导入邮箱账户
func (s *MailboxManagementService) BatchImportAccounts(ctx context.Context, req *database.BatchImportRequest, userID string) (*database.BatchOperationResponse, error) {
	// 生成操作ID
	operationID, err := s.generateOperationID()
	if err != nil {
		return nil, fmt.Errorf("生成操作ID失败: %w", err)
	}

	// 验证导入数据
	if len(req.Accounts) == 0 {
		return nil, fmt.Errorf("导入账户列表不能为空")
	}

	// 检查导入限制
	if len(req.Accounts) > 1000 { // 从配置中读取
		return nil, fmt.Errorf("单次导入账户数量不能超过1000个")
	}

	// 创建批量操作记录
	batchOp := &database.BatchOperation{
		OperationID:   operationID,
		OperationType: "import",
		Status:        "pending",
		TotalCount:    len(req.Accounts),
		CreatedBy:     userID,
		CreatedAt:     time.Now(),
		OperationParams: map[string]interface{}{
			"source":      req.Source,
			"tags":        req.Tags,
			"auto_verify": req.AutoVerify,
			"description": req.Description,
		},
	}

	// 保存批量操作记录
	if err := s.saveBatchOperation(batchOp); err != nil {
		return nil, fmt.Errorf("保存批量操作记录失败: %w", err)
	}

	// 异步执行导入
	go s.executeBatchImport(ctx, operationID, req)

	return &database.BatchOperationResponse{
		OperationID: operationID,
		Status:      "pending",
		Message:     "批量导入任务已创建，正在后台执行",
		TotalCount:  len(req.Accounts),
	}, nil
}

// executeBatchImport 执行批量导入
func (s *MailboxManagementService) executeBatchImport(_ context.Context, operationID string, req *database.BatchImportRequest) {
	// 更新操作状态为运行中
	now := time.Now()
	s.updateBatchOperationStatus(operationID, "running", &now, nil)

	var successCount, failedCount int
	batchImportID := s.generateBatchImportID()

	for i, accountData := range req.Accounts {
		// 检查邮箱格式
		if !s.isValidEmail(accountData.Email) {
			failedCount++
			s.logger.Warn("无效的邮箱格式", "email", accountData.Email)
			continue
		}

		// 检查邮箱是否已存在
		if s.accountExists(accountData.Email) {
			failedCount++
			s.logger.Warn("邮箱已存在", "email", accountData.Email)
			continue
		}

		// 创建账户记录（使用明文密码）
		account := &database.ExtendedAccount{
			Email:              accountData.Email,
			Password:           accountData.Password,
			LoginStatus:        "failed",
			EmailStatus:        "valid",
			UsageStatus:        "available",
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
			BatchImportID:      batchImportID,
			VerificationStatus: "unverified",
			ImportSource:       req.Source,
			Tags:               database.StringSlice(req.Tags),
		}

		// 保存账户
		if err := s.saveExtendedAccount(account); err != nil {
			failedCount++
			s.logger.Error("保存账户失败", "email", accountData.Email, "error", err)
			continue
		}

		successCount++

		// 更新进度
		s.updateBatchOperationProgress(operationID, i+1, successCount, failedCount)
	}

	// 完成操作
	completedAt := time.Now()
	s.updateBatchOperationStatus(operationID, "completed", nil, &completedAt)
	s.updateBatchOperationCounts(operationID, len(req.Accounts), successCount, failedCount)

	// 如果启用自动验证，创建验证任务
	if req.AutoVerify && successCount > 0 {
		s.createVerificationTask(operationID, batchImportID)
	}
}

// GetAccountsList 获取账户列表（支持筛选，优化版本）
func (s *MailboxManagementService) GetAccountsList(ctx context.Context, filter *database.MailboxFilterRequest) (*database.PaginatedResponse[database.ExtendedAccount], error) {
	// 创建带超时的上下文
	queryCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	// 构建查询条件
	query := `SELECT
		a.id, a.email, a.password, a.cookie_data,
		a.login_status, a.last_login_time, a.email_status,
		a.usage_status, a.usage_id, a.session_id,
		a.jsession_id, a.navigator_sid, a.created_at, a.updated_at,
		a.batch_import_id, a.verification_status, a.last_verification_time,
		a.verification_error, a.is_disabled, a.import_source, a.tags
		FROM accounts a WHERE 1=1`

	var args []interface{}
	argIndex := 1

	// 添加筛选条件
	if len(filter.Status) > 0 {
		placeholders := make([]string, len(filter.Status))
		for i, status := range filter.Status {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, status)
			argIndex++
		}
		query += fmt.Sprintf(" AND a.login_status IN (%s)", strings.Join(placeholders, ","))
	}

	if len(filter.VerificationStatus) > 0 {
		placeholders := make([]string, len(filter.VerificationStatus))
		for i, status := range filter.VerificationStatus {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, status)
			argIndex++
		}
		query += fmt.Sprintf(" AND a.verification_status IN (%s)", strings.Join(placeholders, ","))
	}

	// 添加日期范围筛选
	if filter.DateRange != nil {
		query += fmt.Sprintf(" AND a.created_at BETWEEN $%d AND $%d", argIndex, argIndex+1)
		args = append(args, filter.DateRange.StartDate, filter.DateRange.EndDate)
		argIndex += 2
	}

	// 添加排序
	if filter.SortBy != "" {
		order := "ASC"
		if filter.SortOrder == "desc" {
			order = "DESC"
		}
		query += fmt.Sprintf(" ORDER BY a.%s %s", filter.SortBy, order)
	} else {
		query += " ORDER BY a.created_at DESC"
	}

	// 添加分页
	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, filter.PageSize, offset)
	}

	// 执行查询（使用带超时的上下文）
	rows, err := s.db.GetDB().QueryContext(queryCtx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询账户列表失败: %w", err)
	}
	defer rows.Close()

	var accounts []database.ExtendedAccount
	for rows.Next() {
		var account database.ExtendedAccount
		var batchImportID, verificationError, importSource, tagsJSON sql.NullString

		err := rows.Scan(
			&account.ID, &account.Email, &account.Password, &account.CookieData,
			&account.LoginStatus, &account.LastLoginTime, &account.EmailStatus,
			&account.UsageStatus, &account.UsageID, &account.SessionID,
			&account.JSessionID, &account.NavigatorSID, &account.CreatedAt, &account.UpdatedAt,
			&batchImportID, &account.VerificationStatus, &account.LastVerificationTime,
			&verificationError, &account.IsDisabled, &importSource, &tagsJSON,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描账户数据失败: %w", err)
		}

		// 处理可空字段
		if batchImportID.Valid {
			account.BatchImportID = batchImportID.String
		}
		if verificationError.Valid {
			account.VerificationError = verificationError.String
		}
		if importSource.Valid {
			account.ImportSource = importSource.String
		} else {
			account.ImportSource = "manual" // 默认值
		}

		// 解析tags JSON
		if tagsJSON.Valid && tagsJSON.String != "" && tagsJSON.String != "[]" {
			// 简单的JSON解析，实际应该使用json.Unmarshal
			account.Tags = strings.Split(strings.Trim(tagsJSON.String, "[]\""), "\",\"")
		}

		accounts = append(accounts, account)
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM accounts a WHERE 1=1"
	// 重新构建计数查询的WHERE条件（复用上面的逻辑）
	var countArgs []interface{}
	// ... 这里应该复用上面的筛选逻辑

	var total int
	err = s.db.GetDB().QueryRowContext(queryCtx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询账户总数失败: %w", err)
	}

	return &database.PaginatedResponse[database.ExtendedAccount]{
		Items:      accounts,
		Total:      total,
		Page:       filter.Page,
		PageSize:   &filter.PageSize,
		TotalPages: &[]int{(total + filter.PageSize - 1) / filter.PageSize}[0],
	}, nil
}

// StartVerificationTask 启动验证任务
func (s *MailboxManagementService) StartVerificationTask(ctx context.Context, req *database.VerificationTaskRequest, userID string) (*database.BatchOperationResponse, error) {
	operationID, err := s.generateOperationID()
	if err != nil {
		return nil, fmt.Errorf("生成操作ID失败: %w", err)
	}

	// 创建批量操作记录
	batchOp := &database.BatchOperation{
		OperationID:   operationID,
		OperationType: "verify",
		Status:        "pending",
		TotalCount:    len(req.AccountEmails),
		CreatedBy:     userID,
		CreatedAt:     time.Now(),
		OperationParams: map[string]interface{}{
			"verification_type": req.VerificationType,
			"concurrent_limit":  req.ConcurrentLimit,
			"retry_limit":       req.RetryLimit,
		},
	}

	if err := s.saveBatchOperation(batchOp); err != nil {
		return nil, fmt.Errorf("保存批量操作记录失败: %w", err)
	}

	// 异步执行验证（使用增强版本的方法）
	go s.executeVerificationTaskWithLogging(ctx, operationID, "", nil, req)

	return &database.BatchOperationResponse{
		OperationID: operationID,
		Status:      "pending",
		Message:     "验证任务已创建，正在后台执行",
		TotalCount:  len(req.AccountEmails),
	}, nil
}

// GetBatchOperationStatus 获取批量操作状态
func (s *MailboxManagementService) GetBatchOperationStatus(ctx context.Context, operationID string) (*database.BatchOperation, error) {
	query := `SELECT id, operation_id, operation_type, status, total_count, processed_count,
		success_count, failed_count, error_message, created_by, created_at, started_at,
		completed_at, progress_data, operation_params
		FROM batch_operations WHERE operation_id = ?`

	var batchOp database.BatchOperation
	err := s.db.GetDB().QueryRow(query, operationID).Scan(
		&batchOp.ID, &batchOp.OperationID, &batchOp.OperationType, &batchOp.Status,
		&batchOp.TotalCount, &batchOp.ProcessedCount, &batchOp.SuccessCount, &batchOp.FailedCount,
		&batchOp.ErrorMessage, &batchOp.CreatedBy, &batchOp.CreatedAt, &batchOp.StartedAt,
		&batchOp.CompletedAt, &batchOp.ProgressData, &batchOp.OperationParams,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("操作不存在")
		}
		return nil, fmt.Errorf("查询批量操作状态失败: %w", err)
	}

	return &batchOp, nil
}

// ControlTask 控制任务（开始/暂停/停止/重置）
func (s *MailboxManagementService) ControlTask(ctx context.Context, req *database.TaskControlRequest, userID string) error {
	switch req.Action {
	case "start":
		return s.startTask(req.TaskID)
	case "pause":
		return s.pauseTask(req.TaskID)
	case "stop":
		return s.stopTask(req.TaskID)
	case "reset":
		return s.resetTask(req.TaskID)
	default:
		return fmt.Errorf("不支持的操作: %s", req.Action)
	}
}

// GetTaskSchedulerStatus 获取任务调度器状态
func (s *MailboxManagementService) GetTaskSchedulerStatus(ctx context.Context) ([]database.TaskSchedulerStatus, error) {
	query := `SELECT id, task_name, task_type, status, current_operation_id, last_run_at,
		next_run_at, run_count, error_count, last_error, config_data, created_at, updated_at
		FROM task_scheduler_status ORDER BY task_name`

	rows, err := s.db.GetDB().Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询任务调度器状态失败: %w", err)
	}
	defer rows.Close()

	var tasks []database.TaskSchedulerStatus
	for rows.Next() {
		var task database.TaskSchedulerStatus
		var currentOperationID, lastError sql.NullString

		err := rows.Scan(
			&task.ID, &task.TaskName, &task.TaskType, &task.Status, &currentOperationID,
			&task.LastRunAt, &task.NextRunAt, &task.RunCount, &task.ErrorCount, &lastError,
			&task.ConfigData, &task.CreatedAt, &task.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描任务状态数据失败: %w", err)
		}

		// 处理可空字段
		if currentOperationID.Valid {
			task.CurrentOperationID = currentOperationID.String
		}
		if lastError.Valid {
			task.LastError = lastError.String
		}

		tasks = append(tasks, task)
	}

	return tasks, nil
}

// GetMailboxStatistics 获取邮箱统计信息（优化版本）
func (s *MailboxManagementService) GetMailboxStatistics(ctx context.Context) (*database.MailboxStatistics, error) {
	// 创建带超时的上下文
	queryCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 实时计算统计信息
	stats := &database.MailboxStatistics{
		StatDate:  time.Now().Truncate(24 * time.Hour),
		CreatedAt: time.Now(),
	}

	// 使用单个查询获取所有统计信息，减少数据库锁定时间
	today := time.Now().Format("2006-01-02")
	query := `
		SELECT
			COUNT(*) as total_accounts,
			COUNT(CASE WHEN login_status = 'success' THEN 1 END) as active_accounts,
			COUNT(CASE WHEN verification_status = 'verified' THEN 1 END) as verified_accounts,
			COUNT(CASE WHEN login_status = 'failed' THEN 1 END) as failed_accounts,
			COUNT(CASE WHEN is_disabled = 1 THEN 1 END) as disabled_accounts,
			COUNT(CASE WHEN DATE(created_at) = ? THEN 1 END) as new_imports_today
		FROM accounts`

	err := s.db.GetDB().QueryRowContext(queryCtx, query, today).Scan(
		&stats.TotalAccounts,
		&stats.ActiveAccounts,
		&stats.VerifiedAccounts,
		&stats.FailedAccounts,
		&stats.DisabledAccounts,
		&stats.NewImportsToday,
	)
	if err != nil {
		return nil, fmt.Errorf("查询邮箱统计信息失败: %w", err)
	}

	// 计算验证成功率
	if stats.TotalAccounts > 0 {
		stats.VerificationSuccessRate = float64(stats.VerifiedAccounts) / float64(stats.TotalAccounts) * 100
	}

	return stats, nil
}

// DeleteAccount 删除单个账户
func (s *MailboxManagementService) DeleteAccount(ctx context.Context, accountID string) error {
	// 创建带超时的上下文
	deleteCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	query := "DELETE FROM accounts WHERE id = ?"
	result, err := s.db.GetDB().ExecContext(deleteCtx, query, accountID)
	if err != nil {
		return fmt.Errorf("删除账户失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除结果失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("账户不存在")
	}

	s.logger.Info("账户删除成功", "account_id", accountID)
	return nil
}

// ToggleAccountStatus 切换账户状态
func (s *MailboxManagementService) ToggleAccountStatus(ctx context.Context, accountID string, disabled bool) error {
	// 创建带超时的上下文
	updateCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	query := "UPDATE accounts SET is_disabled = ?, updated_at = ? WHERE id = ?"
	result, err := s.db.GetDB().ExecContext(updateCtx, query, disabled, time.Now(), accountID)
	if err != nil {
		return fmt.Errorf("更新账户状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新结果失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("账户不存在")
	}

	statusText := "启用"
	if disabled {
		statusText = "禁用"
	}
	s.logger.Info("账户状态更新成功", "account_id", accountID, "status", statusText)
	return nil
}

// BatchDeleteAccounts 批量删除账户
func (s *MailboxManagementService) BatchDeleteAccounts(ctx context.Context, accountIDs []int) (*database.BatchOperationResponse, error) {
	operationID, err := s.generateOperationID()
	if err != nil {
		return nil, fmt.Errorf("生成操作ID失败: %w", err)
	}

	// 创建批量操作记录
	batchOp := &database.BatchOperation{
		OperationID:   operationID,
		OperationType: "delete",
		Status:        "pending",
		TotalCount:    len(accountIDs),
		CreatedBy:     "system", // TODO: 从上下文获取用户ID
		CreatedAt:     time.Now(),
		OperationParams: map[string]interface{}{
			"account_ids": accountIDs,
		},
	}

	if err := s.createBatchOperation(batchOp); err != nil {
		return nil, fmt.Errorf("创建批量操作记录失败: %w", err)
	}

	// 异步执行批量删除
	go s.executeBatchDelete(context.Background(), operationID, accountIDs)

	return &database.BatchOperationResponse{
		OperationID: operationID,
		Status:      "pending",
		Message:     "批量删除任务已创建，正在后台执行",
		TotalCount:  len(accountIDs),
	}, nil
}

// BatchDisableAccounts 批量禁用账户
func (s *MailboxManagementService) BatchDisableAccounts(ctx context.Context, accountIDs []int) (*database.BatchOperationResponse, error) {
	operationID, err := s.generateOperationID()
	if err != nil {
		return nil, fmt.Errorf("生成操作ID失败: %w", err)
	}

	// 创建批量操作记录
	batchOp := &database.BatchOperation{
		OperationID:   operationID,
		OperationType: "disable",
		Status:        "pending",
		TotalCount:    len(accountIDs),
		CreatedBy:     "system", // TODO: 从上下文获取用户ID
		CreatedAt:     time.Now(),
		OperationParams: map[string]interface{}{
			"account_ids": accountIDs,
		},
	}

	if err := s.createBatchOperation(batchOp); err != nil {
		return nil, fmt.Errorf("创建批量操作记录失败: %w", err)
	}

	// 异步执行批量禁用
	go s.executeBatchDisable(context.Background(), operationID, accountIDs)

	return &database.BatchOperationResponse{
		OperationID: operationID,
		Status:      "pending",
		Message:     "批量禁用任务已创建，正在后台执行",
		TotalCount:  len(accountIDs),
	}, nil
}
